import { useEffect, useState } from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { useGetMembers } from '../api'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
}

export function MembersTable<TData, TValue>({ columns }: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = useState({})
  const [columnVisibility, setColumnVisibility] =
    useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    []
  )
  const [sorting, setSorting] = useState<SortingState>([])
  const [filters, setFilters] = useState<any>({
    search: '',
    domain: '',
    country: "",
    cityInput: "",
    buyerStatus: "",
    userStatus: "",
    affiliateStatus: "",
    onlineStatus: "",
    pictureStatus: "",
    group: ""
  })
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  const { data: { customer: data = [], meta = {} } = {}, refetch }: any = useGetMembers({
    page: pageIndex + 1,
    limit: pageSize,
    ...filters
  })

  // Inject serial numbers into the data for the current page
  const dataWithSerialNumbers = (data || []).map((item: any, idx: number) => ({
    ...item,
    serialNumber: idx + 1 + (pageIndex * pageSize),
  }));


  const table = useReactTable({
    data: dataWithSerialNumbers,
    columns,
    pageCount: data?.meta?.pages ?? -1,
    manualPagination: true,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination: { pageIndex, pageSize },

    },
    onPaginationChange: (updater) => {
      const newState =
        typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
      setPageIndex(newState.pageIndex)
      setPageSize(newState.pageSize)
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  useEffect(() => {
    refetch()
  }, [pageIndex, pageSize, filters.search, filters.domain, filters.country, filters.city, filters.buyerStatus, filters.userStatus, filters.affiliateStatus, filters.onlineStatus, filters.pictureStatus, filters.group])

  const onPageChange = (pageIndex: any) => {
    setPageIndex(pageIndex)
  }

  const onFilterChanged = (filterValues: any, type: any) => { // type = 0 reset or 1 normal
    if (type === 0) {
      setPageIndex(0)
    }
    setFilters(filterValues)
  }

  return (
    <div className='space-y-4'>
      <DataTableToolbar onFilterChanged={onFilterChanged} table={table} />
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <PaginationControls table={table} meta={meta} onPageChange={onPageChange} />
    </div>
  )
}
