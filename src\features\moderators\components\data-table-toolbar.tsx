import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useEffect, useRef, useState } from 'react'
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
import { Command, CommandGroup, CommandItem, CommandInput, CommandEmpty } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList } from '@/features/members/api'
// import { ADD_CITIES, CITY_PLACEHOLDER } from '@/utils/constant'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const statusOptions = [
  'Online',
  'Offline',
]

const moderatorTypeOptions = [
  "WL Moderator",
  'Default Moderator'
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])
  const [filters, setFilters] = useState<{
    search: string;
    moderatorType: string | undefined;
    status: string;
    // country: string;
    // city: string;
    // cityInput: string;
  }>({
    search: '',
    moderatorType: undefined,
    status: '',
    // country: '',
    // city: '',
    // cityInput: '', // for tag input
  })

  const [hasSearched, setHasSearched] = useState(false)
  // const [cityDropdownOpen, setCityDropdownOpen] = useState(false);
  // const cityInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setCountries(countryList?.country || [])
  }, [countryList?.country?.length])

  const handleFilterChange = (
    key: 'search' | 'moderatorType' | 'status',
    value: string | number[] | string[]
  ) => {
    let newValue = value;
    // For multi-selects, always store as comma-separated string
    // status and moderatorType are single select, country is multi-select
    // if (key === 'country') {
    //   if (Array.isArray(value)) {
    //     newValue = value.join(',');
    //   }
    // }
    setFilters((prev) => ({ ...prev, [key]: newValue }));
    console.log({ ...filters, [key]: newValue });
  };

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.search) {
      table.getColumn('name')?.setFilterValue(filters.search)
    }
    if (filters.moderatorType) {
      const moderatorTypeColumn = table.getColumn('moderatorType')
      if (moderatorTypeColumn) {
        moderatorTypeColumn.setFilterValue(filters.moderatorType)
      }
    }
    if (filters.status) {
      const statusColumn = table.getColumn('status')
      if (statusColumn) {
        statusColumn.setFilterValue(filters.status)
      }
    }
    // if (filters.country) {
    //   const selected = filters.country.split(',').map(Number).filter(Boolean);
    //   const countryColumn = table.getColumn('beneficiaryCountry')
    //   if (countryColumn) {
    //     countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: number[]) => {
    //       return filterValue.includes(row.original.beneficiaryCountry)
    //     }
    //     countryColumn.setFilterValue(selected)
    //   }
    // }
    // if (filters.city) {
    //   const selected = filters.city.split(',').filter(Boolean);
    //   const cityColumn = table.getColumn('beneficiaryCity')
    //   if (cityColumn) {
    //     cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
    //       return filterValue.includes(row.original.beneficiaryCity)
    //     }
    //     cityColumn.setFilterValue(selected)
    //   }
    // }
    setHasSearched(true)
    setInitialFilters({ ...filters })
    onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    table.resetColumnFilters()
    const f: any = {
      search: '',
      moderatorType: undefined,
      status: '',
      // country: '',
    }
    setFilters(f)
    setInitialFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const [initialFilters, setInitialFilters] = useState({
    search: '',
    moderatorType: undefined as string | undefined,
    status: '',
    // country: '',
  })

  const hasFilterChanges = Boolean(
    filters.search !== initialFilters.search ||
    filters.moderatorType !== initialFilters.moderatorType ||
    filters.status !== initialFilters.status
    // filters.country !== initialFilters.country
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        <Input
          placeholder='Search by name, email'
          value={filters.search}
          onChange={(event) => handleFilterChange('search', event.target.value)}
          className='h-9 w-[250px]'
        />
        {/* Moderator Type single-select dropdown */}
        <FilterSelect
          value={filters.moderatorType}
          placeholder="Select Moderator Type"
          options={moderatorTypeOptions}
          onChange={(value) => handleFilterChange('moderatorType', value || '')}
          className="w-[200px] bg-card"
        />
        {/* Online Status single-select dropdown */}
        <FilterSelect
          value={filters.status}
          placeholder="Select Online Status"
          options={statusOptions}
          onChange={(value) => handleFilterChange('status', value || '')}
          className="w-[200px] bg-card"
        />
        {/* Multi-select popover for countries */}
        {/* <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.country && "text-muted-foreground"
            )}>
              {filters.country === '' && <span>Select countries</span>}
              {filters.country && filters.country.split(',').filter(Boolean).slice(0, 2).map((id: string) => {
                const country = countries.find((c: any) => c.id === Number(id));
                return (
                  <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                      {country?.name ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      handleFilterChange('country', filters.country.split(',').filter((v: string) => v !== id).join(','))
                    }} />
                  </div>
                )
              })}
              {filters.country && filters.country.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search countries..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedCountries = [
                    ...countries.filter((c: any) => filters.country.split(',').includes(String(c.id))),
                    ...countries.filter((c: any) => !filters.country.split(',').includes(String(c.id))),
                  ];
                  return sortedCountries.map((country: any) => (
                    <CommandItem
                      key={country.id}
                      onSelect={() => {
                        const selected = filters.country.split(',').includes(String(country.id))
                          ? filters.country.split(',').filter((v: string) => v !== String(country.id)).join(',')
                          : [...filters.country.split(',').filter(Boolean), String(country.id)].join(',')
                        handleFilterChange('country', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{country.name}</span>
                      {filters.country.split(',').includes(String(country.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover> */}
        {/* Multi-input for cities with dropdown for selected */}
        {/* <div className="relative w-[200px]">
          <input
            ref={cityInputRef}
            type="text"
            className="w-full border border-input bg-card text-foreground rounded-md px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground"
            placeholder={filters.city === '' ? CITY_PLACEHOLDER : ADD_CITIES}
            value={filters.cityInput || ""}
            onFocus={() => setCityDropdownOpen(true)}
            onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
            onChange={e => handleFilterChange('cityInput', e.target.value)}
            onKeyDown={e => {
              if (
                (e.key === 'Enter' || e.key === ',') &&
                filters.cityInput &&
                !filters.city.split(',').includes(filters.cityInput.trim())
              ) {
                handleFilterChange('city', [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()].join(','));
                handleFilterChange('cityInput', '');
                e.preventDefault();
              }
              if (e.key === 'Backspace' && !filters.cityInput && filters.city) {
                // Remove last city on backspace if input is empty
                handleFilterChange('city', filters.city.split(',').slice(0, -1).join(','));
              }
            }}
          />
          {cityDropdownOpen && filters.city && (
            <div className="absolute left-0 right-0 mt-1 bg-popover border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
              {filters.city.split(',').map((city: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={city}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {city}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer"
                    onMouseDown={e => e.preventDefault()}
                    onClick={() => {
                      handleFilterChange('city', filters.city.split(',').filter((v: string) => v !== city).join(','))
                    }} />
                </div>
              ))}
              {filters.city.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          )}
        </div> */}
        <Button
          onClick={handleSearch}
          className="h-9 px-3"
          disabled={!hasFilterChanges}
        >
          Search
        </Button>

        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-9 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
