/**
 * Route protection utilities for role-based access control
 */

import { redirect } from '@tanstack/react-router';
import { useAuthStore } from '@/stores/authStore';
import { hasRoleAccess, getUserRoles } from './role-based-navigation';
import { END_POINTS } from '@/features/members/utils/constant';

/**
 * Route protection configuration mapping routes to required roles
 */
export const ROUTE_PERMISSIONS = {
  // Settings routes - superadmin and admin only
  [END_POINTS.SETTINGS]: ['superadmin', 'admin'],
  '/settings/account': ['superadmin', 'admin'],
  '/settings/appearance': ['superadmin', 'admin'],
  '/settings/display': ['superadmin', 'admin'],
  '/settings/notifications': ['superadmin', 'admin'],

  // Admin login activity - superadmin and admin only
  '/admin-login-activity': ['superadmin', 'admin'],

  // User management routes
  '/moderators': ['superadmin', 'admin'],
  '/moderators/reply-messages': ['superadmin', 'admin'],
  '/moderators/login-activity': ['superadmin', 'admin'],
  '/moderators/domain': ['superadmin', 'admin'],
  '/moderators/create-moderator': ['superadmin', 'admin'],
  '/moderators/update-moderator': ['superadmin', 'admin'],

  // Members - superadmin, admin, manager
  [END_POINTS.MEMBERS]: ['superadmin', 'admin', 'manager'],
  '/members/profile': ['superadmin', 'admin', 'manager'],
  '/members/update-member': ['superadmin', 'admin', 'manager'],

  // Models - superadmin, admin, manager
  [END_POINTS.MODELS]: ['superadmin', 'admin', 'manager'],
  '/models/profile': ['superadmin', 'admin', 'manager'],
  '/models/create-model': ['superadmin', 'admin', 'manager'],
  '/models/update-model': ['superadmin', 'admin', 'manager'],
  '/models/profile-images': ['superadmin', 'admin', 'manager'],

  // Sessions - superadmin, admin, manager, chat-mod
  [END_POINTS.SESSIONS]: ['superadmin', 'admin', 'manager', 'chat-mod'],
  [END_POINTS.LOBY]: ['superadmin', 'admin', 'manager', 'chat-mod'],
  '/sessions/chat-mod': ['superadmin', 'admin', 'manager', 'chat-mod'],
  '/active-sessions': ['superadmin', 'admin', 'manager', 'chat-mod'],

  // Resources - superadmin, admin, manager
  [END_POINTS.SMILIES]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_SMILEY]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_SMILEY]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.GIFS]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_GIF]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_GIF]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.GIFTS]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_GIFT]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_GIFT]: ['superadmin', 'admin', 'manager'],

  // Content management - superadmin, admin, manager
  [END_POINTS.FLIRT_MESSAGES]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_FLIRT_MESSAGE]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_FLIRT_MESSAGE]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.BOT_MESSAGES]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_BOT_MESSAGE]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_BOT_MESSAGE]: ['superadmin', 'admin', 'manager'],

  // Packages - superadmin, admin, manager
  [END_POINTS.PACKAGES]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.ADD_PACKAGE]: ['superadmin', 'admin', 'manager'],
  [END_POINTS.UPDATE_PACKAGE]: ['superadmin', 'admin', 'manager'],

  // Currencies - superadmin, admin, manager
  [END_POINTS.CURRENCIES]: ['superadmin', 'admin', 'manager'],

  // Announcements - superadmin, admin, manager
  '/announcements': ['superadmin', 'admin', 'manager'],
  '/announcements/affiliates': ['superadmin', 'admin', 'manager'],
  '/announcements/moderators': ['superadmin', 'admin', 'manager'],
  '/announcements/white-labels': ['superadmin', 'admin', 'manager'],

  // Contact us - superadmin, admin, manager
  [END_POINTS.CONTACT_US]: ['superadmin', 'admin', 'manager'],
} as const;

/**
 * Get current user roles from auth store
 */
export function getCurrentUserRoles(): string[] {
  const { user, roles } = useAuthStore.getState().auth;
  
  // First try to get roles from the roles property in auth store
  if (roles && Array.isArray(roles)) {
    return roles;
  }
  
  // Then try to get roles from user object
  return getUserRoles(user);
}

/**
 * Check if current user has access to a specific route
 * @param routePath - The route path to check
 * @returns boolean - true if user has access, false otherwise
 */
export function hasRouteAccess(routePath: string): boolean {
  const userRoles = getCurrentUserRoles();
  const requiredRoles = ROUTE_PERMISSIONS[routePath as keyof typeof ROUTE_PERMISSIONS];
  
  return hasRoleAccess(userRoles, requiredRoles);
}

/**
 * Route guard function to be used in route beforeLoad
 * @param routePath - The route path being accessed
 * @param redirectTo - Optional custom redirect path (defaults to unauthorized page)
 * @returns void or throws redirect
 */
export function requireRoleAccess(routePath: string, redirectTo?: string) {
  const { accessToken } = useAuthStore.getState().auth;
  
  // First check if user is authenticated
  if (!accessToken) {
    throw redirect({
      to: END_POINTS.SIGN_IN,
      search: {
        redirect: routePath,
      },
      replace: true,
    });
  }

  // Then check if user has required roles for this route
  if (!hasRouteAccess(routePath)) {
    throw redirect({
      to: redirectTo || '/unauthorized',
      replace: true,
    });
  }
}

/**
 * Higher-order function to create route protection for specific roles
 * @param requiredRoles - Array of roles required to access the route
 * @param redirectTo - Optional custom redirect path
 * @returns Route guard function
 */
export function createRoleGuard(requiredRoles: string[], redirectTo?: string) {
  return (routePath: string) => {
    const { accessToken } = useAuthStore.getState().auth;
    
    // First check if user is authenticated
    if (!accessToken) {
      throw redirect({
        to: END_POINTS.SIGN_IN,
        search: {
          redirect: routePath,
        },
        replace: true,
      });
    }

    // Then check if user has required roles
    const userRoles = getCurrentUserRoles();
    if (!hasRoleAccess(userRoles, requiredRoles)) {
      throw redirect({
        to: redirectTo || '/unauthorized',
        replace: true,
      });
    }
  };
}

/**
 * Predefined role guards for common use cases
 */
export const roleGuards = {
  superAdminOnly: createRoleGuard(['superadmin']),
  adminOnly: createRoleGuard(['superadmin', 'admin']),
  managerAndAbove: createRoleGuard(['superadmin', 'admin', 'manager']),
  chatModAndAbove: createRoleGuard(['superadmin', 'admin', 'manager', 'chat-mod']),
  allRoles: createRoleGuard(['superadmin', 'admin', 'manager', 'cashier', 'chat-mod']),
};

/**
 * Get user-friendly error message for unauthorized access
 * @param routePath - The route that was attempted to access
 * @param userRoles - Current user roles
 * @returns Error message string
 */
export function getUnauthorizedMessage(routePath: string, userRoles: string[]): string {
  const requiredRoles = ROUTE_PERMISSIONS[routePath as keyof typeof ROUTE_PERMISSIONS];
  
  if (!requiredRoles) {
    return 'Access denied. This page requires special permissions.';
  }
  
  const roleList = requiredRoles.join(', ');
  const currentRoleList = userRoles.length > 0 ? userRoles.join(', ') : 'none';
  
  return `Access denied. This page requires one of the following roles: ${roleList}. Your current roles: ${currentRoleList}.`;
}
